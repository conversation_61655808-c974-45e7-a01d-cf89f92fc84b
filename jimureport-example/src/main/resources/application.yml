spring:
  profiles:
    active: @profiles.active@


mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.jeecg.**.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis-config.xml
  global-config:
    # 全局配置
    db-config:
      # 数据库配置
      id-type: auto

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql