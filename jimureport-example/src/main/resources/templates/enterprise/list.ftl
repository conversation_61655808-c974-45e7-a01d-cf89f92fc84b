<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业信息管理 - 积木报表</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .search-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .enterprise-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .enterprise-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .enterprise-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0;
        }
        .enterprise-body {
            padding: 20px;
        }
        .enterprise-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .enterprise-code {
            color: #6c757d;
            font-size: 0.9em;
        }
        .enterprise-info {
            margin-top: 15px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .info-label {
            color: #6c757d;
            font-weight: 500;
        }
        .info-value {
            color: #495057;
            font-weight: 600;
        }
        .btn-view-reports {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.9em;
            transition: all 0.2s;
        }
        .btn-view-reports:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            transform: scale(1.05);
        }
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .no-data {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .search-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            transition: all 0.2s;
        }
        .search-btn:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
            transform: scale(1.05);
        }
        .reset-btn {
            background: #6c757d;
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            margin-left: 10px;
            transition: all 0.2s;
        }
        .reset-btn:hover {
            background: #5a6268;
            color: white;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-building"></i> 企业信息管理</h1>
                    <p class="mb-0">管理企业信息，查看企业填报报表</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/jmreport/list" class="btn btn-outline-light">
                        <i class="fas fa-chart-bar"></i> 报表工作台
                    </a>
                    <a href="/drag/list" class="btn btn-outline-light ml-2">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘工作台
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 搜索区域 -->
        <div class="search-card">
            <form id="searchForm">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="enterpriseName">企业名称</label>
                            <input type="text" class="form-control" id="enterpriseName" name="enterpriseName" placeholder="请输入企业名称">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="enterpriseType">企业类型</label>
                            <select class="form-control" id="enterpriseType" name="enterpriseType">
                                <option value="">请选择类型</option>
                                <option value="装备制造">装备制造</option>
                                <option value="化工">化工</option>
                                <option value="电子信息">电子信息</option>
                                <option value="生物医药">生物医药</option>
                                <option value="新材料">新材料</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 text-center">
                        <button type="submit" class="btn search-btn">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <button type="button" class="btn reset-btn" onclick="resetForm()">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 企业列表区域 -->
        <div id="enterpriseList">
            <div class="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-3">正在加载企业信息...</p>
            </div>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-wrapper">
            <nav aria-label="企业列表分页">
                <ul class="pagination" id="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 企业报表模态框 -->
    <div class="modal fade" id="reportsModal" tabindex="-1" role="dialog" aria-labelledby="reportsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reportsModalLabel">
                        <i class="fas fa-file-alt"></i> 企业报表列表
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="reportsModalBody">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-3">正在加载报表信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.2/js/bootstrap.bundle.min.js"></script>
    <script>
        // 当前页面
        let pageNum = 1;
        // 每页显示数量
        let pageSize = 10;
        // 总记录数
        let total = 0;

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadEnterprises(1);
            
            // 搜索表单提交
            // $('#searchForm').on('submit', function(e) {
            //     e.preventDefault();
            //     pageNum = 1;
            //     loadEnterprises(1);
            // });
        });

        // 加载企业列表
        function loadEnterprises(page) {
            pageNum = page;
            const formData = $('#searchForm').serialize();
            
            $.ajax({
                url: '/enterprise/list',
                type: 'GET',
                dataType: 'json',
                data: formData + '&pageNum=' + page + '&pageSize=' + pageSize,
                success: function(response) {
                    if (response.code === 200) {
                        total = response.total;
                        renderEnterpriseList(response.rows);
                        renderPagination();
                    } else {
                        showError('加载企业信息失败：' + response.msg);
                    }
                },
                error: function() {
                    showError('网络错误，请稍后重试');
                }
            });
        }

        // 渲染企业列表
        function renderEnterpriseList(enterprises) {
            const listContainer = $('#enterpriseList');
            
            if (!enterprises || enterprises.length === 0) {
                listContainer.html(`
                    <div class="no-data">
                        <i class="fas fa-inbox fa-3x"></i>
                        <p class="mt-3">暂无企业信息</p>
                    </div>
                `);
                return;
            }

            let html = '<div class="row">';
            enterprises.forEach(function(enterprise) {
                html += `
                    <div class="col-md-6 col-lg-4">
                        <div class="enterprise-card" onclick="viewEnterpriseReports('` + enterprise.id + `', '` + enterprise.name + `')">
                            <div class="enterprise-header">
                                <div class="enterprise-name">` + enterprise.name + `</div>
                                <div class="enterprise-code">ID：` + enterprise.id + `</div>
                            </div>
                            <div class="enterprise-body">
                                <div class="enterprise-info">
                                    <div class="info-item">
                                        <span class="info-label">企业类型：</span>
                                        <span class="info-value">` + (enterprise.type || '未设置') + `</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">排序：</span>
                                        <span class="info-value">` + (enterprise.sort || 0) + `</span>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <button class="btn btn-view-reports">
                                        <i class="fas fa-eye"></i> 查看报表
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            listContainer.html(html);
        }

        // 渲染分页
        function renderPagination() {
            let pages = Math.ceil(total / pageSize);


            const pagination = $('#pagination');

            if (pageNum <= 1) {
                pagination.empty();
                return;
            }

            let html = '';

            // 上一页
            if (pageNum > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadEnterprises(` + (pageNum - 1) + ` )">上一页</a></li>`;
            }

            // 页码
            const startPage = Math.max(1, pageNum - 2);
            const endPage = Math.min(pages, pageNum + 2);

            if (startPage > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadEnterprises(1)">1</a></li>`;
                if (startPage > 2) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                if (i === pageNum) {
                    html += `<li class="page-item active"><span class="page-link">`+i+`</span></li>`;
                } else {
                    html += `<li class="page-item"><a class="page-link" href="#" onclick="loadEnterprises(`+i+`)">`+i+`</a></li>`;
                }
            }

            if (endPage < pages) {
                if (endPage < pages - 1) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadEnterprises(` + pages + `)">` + pages + `</a></li>`;
            }

            // 下一页
            if (pageNum < pages) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadEnterprises(` + (pageNum + 1) + `)">下一页</a></li>`;
            }

            pagination.html(html);
        }

        // 查看企业报表
        function viewEnterpriseReports(enterpriseId, enterpriseName) {
            // 直接跳转到企业报表页面
            window.location.href = '/enterprise/reports?enterpriseId=' + enterpriseId;
        }

        // 渲染报表列表
        function renderReportsList(reports) {
            const modalBody = $('#reportsModalBody');
            
            if (!reports || reports.length === 0) {
                modalBody.html(`
                    <div class="text-center text-muted">
                        <i class="fas fa-inbox fa-3x"></i>
                        <p class="mt-3">该企业暂无填报报表</p>
                    </div>
                `);
                return;
            }

            let html = '<div class="list-group">';
            reports.forEach(function(report) {
                html += `
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">` + report.name + `</h6>
                            <small class="text-muted">` + report.createTime + `</small>
                        </div>
                        <p class="mb-1 text-muted">` + (report.description || '暂无描述') + `</p>
                        <div class="mt-2">
                            <a href="/jmreport/view/` + report.id + `" target="_blank" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye"></i> 查看报表
                            </a>
                            <a href="/jmreport/exportExcel/` + report.id + `" class="btn btn-sm btn-success ml-2">
                                <i class="fas fa-download"></i> 导出Excel
                            </a>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            modalBody.html(html);
        }

        // 重置表单
        function resetForm() {
            $('#searchForm')[0].reset();
            pageNum = 1;
            loadEnterprises(1);
        }

        // 显示错误信息
        function showError(message) {
            $('#enterpriseList').html(
                '<div class="text-center text-danger">'
                +'    <i class="fas fa-exclamation-triangle fa-3x"></i>'
                +'    <p class="mt-3">' + message + '</p>'
                +'</div>');
        }
    </script>
</body>
</html>
