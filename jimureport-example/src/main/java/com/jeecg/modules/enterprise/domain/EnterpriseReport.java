package com.jeecg.modules.enterprise.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业报表实体类
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class EnterpriseReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报表ID
     */
    private String id;

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 报表编码
     */
    private String code;

    /**
     * 报表类型
     */
    private String type;

    /**
     * 报表分类
     */
    private String category;

    /**
     * 报表描述
     */
    private String description;

    /**
     * 报表模板ID
     */
    private String templateId;

    /**
     * 报表模板名称
     */
    private String templateName;

    /**
     * 报告期
     */
    private String reportPeriod;

    /**
     * 报告年度
     */
    private Integer reportYear;

    /**
     * 报告月份
     */
    private Integer reportMonth;

    /**
     * 报告季度
     */
    private Integer reportQuarter;

    /**
     * 填报状态（0：未填报，1：已填报，2：已审核，3：已驳回）
     */
    private Integer status;

    /**
     * 填报进度（百分比）
     */
    private Integer progress;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 报表文件路径
     */
    private String filePath;

    /**
     * 报表文件名
     */
    private String fileName;

    /**
     * 报表文件大小
     */
    private Long fileSize;

    /**
     * 报表URL
     */
    private String reportUrl;

    /**
     * 预览URL
     */
    private String previewUrl;

    /**
     * 下载URL
     */
    private String downloadUrl;

    /**
     * 填报开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 填报结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 审核意见
     */
    private String auditComment;

    /**
     * 填报人
     */
    private String reportBy;

    /**
     * 填报人姓名
     */
    private String reportByName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0：正常，1：删除）
     */
    private Integer delFlag;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 是否必填（0：否，1：是）
     */
    private Integer required;

    /**
     * 是否公开（0：否，1：是）
     */
    private Integer isPublic;

    /**
     * 标签
     */
    private String tags;
}
