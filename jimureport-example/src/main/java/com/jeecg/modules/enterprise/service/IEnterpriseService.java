package com.jeecg.modules.enterprise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jeecg.modules.enterprise.domain.Enterprise;
import com.jeecg.modules.enterprise.domain.EnterpriseReport;

import java.util.List;
import java.util.Map;

/**
 * 企业信息服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface IEnterpriseService extends IService<Enterprise> {

    /**
     * 获取企业信息列表
     * 
     * @param query 查询参数
     * @return 企业信息列表
     */
    List<Enterprise> getEnterpriseList(Enterprise query);

    /**
     * 根据ID获取企业信息
     * 
     * @param enterpriseId 企业ID
     * @return 企业信息
     */
    Enterprise getEnterpriseById(String enterpriseId);

    /**
     * 获取企业报表列表
     * 
     * @param enterpriseId 企业ID
     * @return 企业报表列表
     */
    List<EnterpriseReport> getEnterpriseReports(String enterpriseId);

    /**
     * 获取行业列表
     * 
     * @return 行业列表
     */
    List<String> getIndustries();

    /**
     * 获取企业统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getStatistics();

    /**
     * 新增企业信息
     * 
     * @param enterprise 企业信息
     * @return 是否成功
     */
    boolean addEnterprise(Enterprise enterprise);

    /**
     * 更新企业信息
     * 
     * @param enterprise 企业信息
     * @return 是否成功
     */
    boolean updateEnterprise(Enterprise enterprise);

    /**
     * 删除企业信息
     * 
     * @param enterpriseId 企业ID
     * @return 是否成功
     */
    boolean deleteEnterprise(String enterpriseId);

    /**
     * 批量删除企业信息
     * 
     * @param enterpriseIds 企业ID列表
     * @return 是否成功
     */
    boolean batchDeleteEnterprises(List<String> enterpriseIds);

    /**
     * 获取企业报表详情
     * 
     * @param reportId 报表ID
     * @return 报表详情
     */
    EnterpriseReport getEnterpriseReportById(String reportId);

    /**
     * 更新企业报表数量
     * 
     * @param enterpriseId 企业ID
     */
    void updateEnterpriseReportCount(String enterpriseId);

    /**
     * 检查企业代码是否存在
     * 
     * @param enterpriseCode 企业代码
     * @param excludeId 排除的企业ID
     * @return 是否存在
     */
    boolean checkEnterpriseCodeExists(String enterpriseCode, String excludeId);

    /**
     * 检查统一社会信用代码是否存在
     * 
     * @param creditCode 统一社会信用代码
     * @param excludeId 排除的企业ID
     * @return 是否存在
     */
    boolean checkCreditCodeExists(String creditCode, String excludeId);
}
